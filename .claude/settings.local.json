{"permissions": {"allow": ["<PERSON><PERSON>(jbang:*)", "Bash(npm install:*)", "WebSearch", "WebFetch(domain:github.com)", "Bash(uvx:*)", "Bash(export PATH=\"$HOME/.local/bin:$PATH\")", "Bash(claude code /mcp)", "Bash(rm:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(cp:*)", "<PERSON><PERSON>(camel run:*)", "<PERSON><PERSON>(cat:*)", "mcp__context7__get-library-docs"], "defaultMode": "acceptEdits"}}