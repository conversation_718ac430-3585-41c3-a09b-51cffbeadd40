# Kafka Configuration
source.consumer.topic.name=oneapp.payment.partner-payment-status
source.consumer.group=best-group

kafka.brokers=stmoneamqd1.tmbbank.local:9092
# In production, SSL/TLS is required. For local dev, you can comment these out.
# kafka.security.protocol=SSL
# kafka.ssl.keystore.location=/oneapp-dev.tmbbank.local.jks
# kafka.ssl.truststore.location=/oneapp-dev.tmbbank.local.jks
# kafka.ssl.keystore.password=changeit
# kafka.ssl.truststore.password=changeit

# Activity Logging Topic
kafka.prefix.topic=dev_
kafka.activity.topic=activity

# Partner Configuration
# List of partners (comma-separated)
partners.list=lineman,shopee,internal

# Global SSL Configuration for Partners (Disabled for now)
# partners.ssl.keystore.location=/path/to/your/partners.jks
# partners.ssl.keystore.password=your-keystore-password

partners.lineman.token.url=https://localhost:8443/oauth/token
partners.lineman.client.id=test-client-id
partners.lineman.client.secret=test-client-secret
# partners.lineman.ssl.alias=lineman-cert

partners.shopee.token.url=https://localhost:8443/oauth/token
partners.shopee.client.id=test-client-id
partners.shopee.client.secret=test-client-secret
# Assuming shopee also needs SSL
# partners.shopee.ssl.alias=shopee-cert

# Partner 'internal' does not require token authentication.
# No token configuration needed for this partner.

camel.ssl.config.trust-store-file=truststore.jks
camel.ssl.config.trust-store-password=changeit
camel.ssl.config.trust-store-type=JKS
