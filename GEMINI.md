# Project Overview

This project is a Java-based integration application built using Apache Camel. Its primary purpose is to consume payment status update messages from a Kafka topic and forward these updates to configured partner APIs via HTTP POST requests. It handles both standard partner integrations and internal system integrations.

Key features include:
- Consuming messages from a Kafka topic.
- Authentication with partner APIs using OAuth 2.0 Client Credentials flow, with token caching.
- Dynamic routing of HTTP requests based on URLs provided within the Kafka message payload.
- Activity logging by sending status information (success/failure) to a dedicated Kafka activity topic.
- Configuration-driven setup via an `application.properties` file.

The application is designed to be run using JBang, which allows executing Java code directly without a separate build step.

## Building and Running

This project uses JBang for execution, eliminating the need for a traditional build tool like Maven or Gradle for running.

**Prerequisites:**
- Java JDK (version 8 or higher)
- JBang installed (`jbang` command available in PATH)

**Running the Application:**
1. Ensure Kafka is running and accessible with the details configured in `application.properties`.
2. Verify the `application.properties` file contains the correct configuration for Kafka brokers, topics, and partner API credentials.
3. Navigate to the project directory in your terminal.
4. Execute the main Java file using JBang:
   ```bash
   jbang PaymentStatusCallback.java
   ```
   or
   ```bash
   camel run PaymentStatusCallback.java
   ```

JBang will automatically download the required dependencies specified in the `//DEPS` comments within `PaymentStatusCallback.java`, compile the code, and run the application.

## Development Conventions

- **Configuration**: All external configuration is managed through the `application.properties` file. Avoid hardcoding values directly in the Java code.
- **Dependencies**: Dependencies are declared using JBang's `//DEPS` directive at the top of the `PaymentStatusCallback.java` file.
- **Routing Logic**: Camel routes are defined within the `configure()` method of the `PaymentStatusCallback` class.
- **Error Handling**: Uses Camel's `onException`, `doTry`, `doCatch`, and `doFinally` blocks for handling exceptions during HTTP calls and ensuring activity logs are sent regardless of the outcome.
- **Logging**: Uses SLF4J for logging messages at appropriate levels (info, warn, error).
- **Caching**: Uses Caffeine to cache OAuth tokens to avoid repeated authentication requests for the same partner.
