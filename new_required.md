# Required Configurations and Setup

This document outlines the required configurations and setup steps for the Camel Payment Status Callback application.

## Prerequisites

1. Java JDK 8 or higher
2. JBang installed (`jbang` command available in PATH)
3. Kafka cluster accessible with SSL support
4. Partner APIs with OAuth 2.0 Client Credentials flow support

## Required Configuration Files

### 1. `application.properties`

The main configuration file that must be properly set up with the following properties:

#### Kafka Configuration
```properties
# Source consumer topic and group
source.consumer.topic.name=your-topic-name
source.consumer.group=your-group-name

# Kafka brokers
kafka.brokers=your-kafka-brokers:9092

# SSL configuration (if required)
kafka.security.protocol=SSL
kafka.ssl.keystore.location=/path/to/keystore.jks
kafka.ssl.truststore.location=/path/to/truststore.jks
kafka.ssl.keystore.password=keystore-password
kafka.ssl.truststore.password=truststore-password
```

#### Activity Logging Configuration
```properties
# Kafka topic prefix (optional)
kafka.prefix.topic=prefix-

# Activity logging topic
kafka.activity.topic=activity-topic-name
```

#### Partner Configuration
```properties
# Comma-separated list of partner identifiers
partners.list=partner1,partner2

# For each partner, configure the following properties:
# Note: Token configuration is optional for partners that don't require authentication
partners.{partnerId}.token.url=https://partner-api.com/oauth/token
partners.{partnerId}.client.id=your-client-id
partners.{partnerId}.client.secret=your-client-secret
```

Example for a partner named "lineman" (with token authentication):
```properties
partners.lineman.token.url=https://lineman-api.com/oauth/token
partners.lineman.client.id=lineman-client-id
partners.lineman.client.secret=lineman-client-secret
```

Example for a partner named "internal" (without token authentication):
```properties
# No token configuration needed for this partner
partners.internal=
```

Some partners may not require token authentication. For these partners, you can either:
1. Omit the token configuration entirely
2. Leave the token configuration empty as shown in the "internal" example above

The application will skip the token retrieval process for partners that don't have token configuration defined.

## Running the Application

1. Ensure Kafka is running and accessible with the details configured in `application.properties`
2. Verify the `application.properties` file contains the correct configuration
3. Navigate to the project directory in your terminal
4. Execute the main Java file using one of the following commands:
   ```bash
   # Using jbang
   jbang PaymentStatusCallback.java

   # Or using Camel JBang
   camel run PaymentStatusCallback.java
   ```

JBang will automatically download the required dependencies, compile the code, and run the application.

## Expected Kafka Message Format

The application expects messages from Kafka with the following JSON structure:
```json
{
  "url": "https://partner-api.com/callback",
  "data": {
    // Payment status data to be sent to the partner
  }
}
```

The message must also include an `entry-id` header that exactly matches one of the partners configured in the `partners.list` property. For example, if your `partners.list` contains `lineman,internal`, then the `entry-id` header must be either `lineman` or `internal` for the message to be processed.

## Message Processing Workflow

The application processes messages based on the configured partners in `partners.list`. It creates a dedicated Kafka consumer with a **unique consumer group** for each partner to ensure maximum isolation.

1.  **Dynamic Consumer and Group Creation**: The application reads the `partners.list` from the properties file. For each partner in the list, it dynamically creates and starts a new Kafka consumer route. A unique `groupId` is generated for each consumer by appending the partner's name to the `source.consumer.group` property (e.g., `your-group-name-partner1`).

2.  **Broadcast-based Consumption**: Because each consumer is in its own group, every message on the topic is broadcast to every consumer. This provides complete processing isolation between partners.

3.  **Message Filtering**: Each consumer receives all messages from the topic and is responsible for filtering them. It will only process messages where the `entry-id` header exactly matches its assigned partner's name. All other messages are ignored by that consumer.

4.  **Processing Logic**: For a valid message, the application will:
    *   Remove all incoming message headers to avoid leaking internal information.
    *   Retrieve an OAuth token if configured for that partner (skipped for partners without token configuration).
    *   Send the payment status data to the URL specified in the message payload.
    *   Log the result (success or failure) to the activity topic.

**Note on this approach:** While this architecture provides strong isolation, it is less efficient than using a shared consumer group. Every message is delivered to every consumer instance, increasing network traffic and consumer-side processing for filtering. For this use case, this trade-off was made to ensure that slow partners do not affect the processing of others under any circumstances.

## SSL Certificate Requirements

If using SSL for Kafka connections, ensure that:
1. The keystore and truststore files exist at the specified locations
2. The passwords are correct
3. The certificates are valid and trusted by the Kafka cluster

## Token Caching Behavior

The application uses Caffeine caching for OAuth tokens to avoid repeated authentication requests. The cache expiration is dynamically set based on the `expires_in` field (in seconds) returned in the OAuth token response, rather than using a fixed expiration time.

Tokens are loaded on demand when processing messages for a partner, rather than pre-loading all tokens at application startup. This approach has several benefits:
- Reduces startup time by only loading tokens when needed
- Avoids failures during startup if some partner APIs are temporarily unavailable
- Ensures tokens are as fresh as possible when messages arrive

When a token is requested:
1. The application first checks the cache for a valid token
2. If not found or expired, it requests a new token from the partner's OAuth endpoint
3. The token is stored in the cache with an expiration time based on the `expires_in` value from the response
4. Tokens are automatically evicted from the cache after their specified expiration time

This approach ensures that tokens are refreshed appropriately based on their actual expiration time as defined by the OAuth provider, rather than using a fixed time period.

## Token Refresh on 401 Unauthorized

For partners that require OAuth tokens, if the main API call returns an HTTP 401 Unauthorized status, the application will automatically:
1. Invalidate the cached token for that partner
2. Request a new token from the OAuth endpoint
3. Retry the API call once with the new token

This retry mechanism helps handle cases where tokens have expired or been invalidated between the time they were cached and when the API call is made. The retry is limited to one attempt to prevent infinite loops in case of persistent authentication issues.

## Additional Implementation Details

1. **Activity Logging**: The application only logs success or failure status to the activity topic. This is sufficient for monitoring the application's behavior. No detailed error information from HTTP responses is included in the logs.

2. **Partner Configuration Validation**: If a partner is listed in `partners.list` but does not have `token.url`, `client.id`, or `client.secret` properties, it means that partner does not require OAuth authentication. The application will directly call the main API without attempting to retrieve a token.

3. **Rate Limiting**: Currently, there is no rate limiting implemented for calls to partner APIs.

4. **Activity Topic Failure Handling**: If sending a message to the activity topic fails, the application will simply do nothing and let the operation fail silently. There is no retry mechanism or dead letter queue for activity logging.

5. **Health Checks**: The application does not expose any health check endpoints or metrics for monitoring its status.

6. **Cache Size Limits**: There is no limit to the number of tokens that can be cached in the Caffeine cache. The cache will grow as needed based on the number of partners.

7. **HTTP Timeout Configuration**: Currently, there are no configurable timeout values for HTTP requests to partner APIs or OAuth token endpoints. The application uses the default timeout values provided by the underlying HTTP client.

8. **Kafka Message Processing**: The application relies on external mechanisms to ensure Kafka is working properly. There are no specific message processing guarantees implemented within the application itself.

9. **Configuration Validation**: The application does not validate the `application.properties` configuration at startup. Configuration issues will only be discovered when processing messages that require the missing configuration.

10. **SSL/TLS Configuration**: In production environments, SSL/TLS is always required for Kafka connections. However, in development environments, SSL/TLS can be omitted for easier local testing.