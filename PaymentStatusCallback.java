///usr/bin/env jbang "$0" "$@" ; exit $?
//DEPS org.apache.camel:camel-core:4.13.0
//DEPS org.apache.camel:camel-kafka:4.13.0
//DEPS org.apache.camel:camel-http:4.13.0
//DEPS org.apache.camel:camel-jackson:4.13.0
//DEPS org.apache.camel:camel-caffeine:4.13.0
//DEPS org.apache.camel:camel-jsonpath:4.13.0
//DEPS org.apache.camel:camel-support:4.13.0
//DEPS org.slf4j:slf4j-api:2.0.7
//DEPS org.slf4j:slf4j-simple:2.0.7

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Expiry;
import org.apache.camel.Exchange;
import org.apache.camel.LoggingLevel;
import org.apache.camel.Processor;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.http.base.HttpOperationFailedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.apache.camel.Exchange.*;
import static org.apache.camel.model.dataformat.JsonLibrary.Jackson;

public class PaymentStatusCallback extends RouteBuilder {

    private static final Logger log = LoggerFactory.getLogger(PaymentStatusCallback.class);

    @Override
    public void configure() throws Exception {

        getContext().getPropertiesComponent().setLocation("file:application.properties");

        // Configure SSL for HTTPS requests
        configureSSL();

        onException(HttpOperationFailedException.class)
            .onWhen(simple("${exception.statusCode} == 401"))
            .maximumRedeliveries(1)
            .redeliveryDelay(1000)
            .onRedelivery(exchange -> {
                String partnerId = exchange.getProperty("EntryId", String.class);
                log.warn("Partner API call returned 401. Refreshing token before retry for partner: {}.", partnerId);
                if (partnerId != null) {
                    try {
                        refreshToken(partnerId.trim());
                    } catch (Exception e) {
                        log.error("Failed to refresh token for partner: {}. Aborting retry.", partnerId, e);                    
                        exchange.setProperty("FailureException", e);
                        String failReason = "Token refresh failed";
                        if (e != null && e.getMessage() != null) {
                            failReason += ": " + e.getMessage();
                        }
                        exchange.setProperty("FailureReason", failReason);
                        throw new RuntimeException("Token refresh failed, aborting redelivery", e);
                    }
                }
            })
            .log(LoggingLevel.ERROR, "All retries failed for partner: ${exchangeProperty.EntryId}. The message will be marked as Failed.")
            .handled(false);

        String partnersList = getContext().resolvePropertyPlaceholders("{{partners.list}}");
        if (partnersList != null && !partnersList.isEmpty()) {
            List<String> partners = Arrays.asList(partnersList.split(","));
            for (String partner : partners) {
                partner = partner.trim();
                
                from("kafka:{{kafka.prefix.topic}}{{source.consumer.topic.name}}?brokers={{kafka.brokers}}" +
                        "&groupId={{source.consumer.group}}-" + partner +
                        "&securityProtocol={{?kafka.security.protocol}}" +
                        "&sslKeystoreLocation={{?kafka.ssl.keystore.location}}" +
                        "&sslKeystorePassword={{?kafka.ssl.keystore.password}}" +
                        "&sslTruststoreLocation={{?kafka.ssl.truststore.location}}" +
                        "&sslTruststorePassword={{?kafka.ssl.truststore.password}}")
                    .routeId("kafka-consumer-route-" + partner)
                    .filter(header("entry_id").isEqualTo(partner))
                    .to("direct:process-payment");
            }
        }

        from("direct:process-payment")
            .routeId("process-payment-route")
            .log("Processing message for partner ${header.entry_id}: ${body}")
            .setProperty("CallbackUrl", jsonpath("$.url"))
            .setProperty("EntryId", simple("${header.entry_id}"))
            .setBody(jsonpath("$.data"))
            .marshal().json(Jackson)
            .doTry()
                .to("direct:call-partner-api")
                .process(exchange -> exchange.getIn().setHeader("paymentStatus", "Success"))
            .doCatch(Exception.class)
                .process(exchange -> {
                    exchange.getIn().setHeader("paymentStatus", "Failed");
                    Exception caughtException = exchange.getProperty(EXCEPTION_CAUGHT, Exception.class);
                    exchange.setProperty("FailureException", caughtException);                
                    String failReason = "Partner API call failed";
                    if (caughtException != null && caughtException.getMessage() != null) {
                        failReason += ": " + caughtException.getMessage();
                    }
                    exchange.setProperty("FailureReason", failReason);
                    log.error("Partner API call failed for partner ${header.entry_id}", caughtException);
                })
            .doFinally()
                .process(this::sendActivityLog)
            .end();

        from("direct:call-partner-api")
            .routeId("call-partner-api-route")
            .removeHeaders("*")
            .process(this::setAuthorizationHeader)
            .setHeader(HTTP_METHOD, constant("POST"))
            .setHeader(CONTENT_TYPE, constant("application/json"))
            .log("Sending request to ${exchangeProperty.CallbackUrl} with payload ${body}")
            .toD("${exchangeProperty.CallbackUrl}");

        from("direct:getToken")
            .routeId("get-token-route")
            .setHeader(HTTP_METHOD, constant("POST"))
            .setHeader(CONTENT_TYPE, constant("application/x-www-form-urlencoded"))
            .toD("${header.tokenUrl}")
            .unmarshal().json(Jackson, Map.class);
        
        from("direct:activityLog")
            .routeId("activity-log-route")
            .marshal().json(Jackson)
            .log("Sending activity log: ${body} to topic ${header.activityTopic}")
            .toD("kafka:${header.activityTopic}?brokers={{kafka.brokers}}" +
                    "&securityProtocol={{?kafka.security.protocol}}" +
                    "&sslKeystoreLocation={{?kafka.ssl.keystore.location}}" +
                    "&sslKeystorePassword={{?kafka.ssl.keystore.password}}" +
                    "&sslTruststoreLocation={{?kafka.ssl.truststore.location}}" +
                    "&sslTruststorePassword={{?kafka.ssl.truststore.password}}")
            .onException(Exception.class)
                .handled(true)
                .log(LoggingLevel.ERROR, "Failed to send message to activity topic. Error: ${exception.message}");
    }

    private void configureSSL() throws Exception {
        // Get SSL configuration from properties
        String trustStoreFile = getContext().resolvePropertyPlaceholders("{{camel.ssl.config.trust-store-file}}");
        String trustStorePassword = getContext().resolvePropertyPlaceholders("{{camel.ssl.config.trust-store-password}}");
        String trustStoreType = getContext().resolvePropertyPlaceholders("{{camel.ssl.config.trust-store-type}}");

        boolean isTrustStoreConfigured = trustStoreFile != null && !trustStoreFile.trim().isEmpty()
                                       && !trustStoreFile.equals("{{camel.ssl.config.trust-store-file}}");

        if (isTrustStoreConfigured) {
            log.info("Configuring SSL with truststore: {}", trustStoreFile);

            try {
                // Set system properties for SSL truststore
                // This approach is simpler and more reliable than programmatic configuration
                System.setProperty("javax.net.ssl.trustStore", trustStoreFile);
                System.setProperty("javax.net.ssl.trustStorePassword", trustStorePassword);
                System.setProperty("javax.net.ssl.trustStoreType", trustStoreType);

                log.info("SSL configuration completed successfully using system properties");
                log.info("Truststore: {}", trustStoreFile);
                log.info("Truststore type: {}", trustStoreType);
            } catch (Exception e) {
                log.error("Failed to configure SSL: {}", e.getMessage(), e);
                throw e;
            }
        } else {
            log.warn("SSL truststore not configured. HTTPS requests may fail with certificate validation errors.");
        }
    }

    private Token refreshToken(String partnerId) throws Exception {
        log.info("Explicitly refreshing token for partner: {}", partnerId);
        
        String tokenUrl = getContext().resolvePropertyPlaceholders("{{partners." + partnerId + ".token.url:}}");
        if (tokenUrl == null || tokenUrl.trim().isEmpty()) {
            return null;
        }

        String clientId = getContext().resolvePropertyPlaceholders("{{partners." + partnerId + ".client.id}}");
        String clientSecret = getContext().resolvePropertyPlaceholders("{{partners." + partnerId + ".client.secret}}");
        String requestBody = "grant_type=client_credentials&client_id=" + clientId + "&client_secret=" + clientSecret;

        Map<String, Object> response = getContext().createProducerTemplate()
            .requestBodyAndHeader("direct:getToken", requestBody, "tokenUrl", tokenUrl, Map.class);

        String accessToken = (String) response.get("access_token");
        long expiresIn = ((Number) response.getOrDefault("expires_in", 3600)).longValue();

        Token newToken = new Token(accessToken, expiresIn);
        tokenCache.put(partnerId, newToken);
        return newToken;
    }

    private void setAuthorizationHeader(Exchange exchange) throws Exception {
        String partnerId = exchange.getProperty("EntryId", String.class).trim();
        String tokenUrl = getContext().resolvePropertyPlaceholders("{{partners." + partnerId + ".token.url:}}");

        if (tokenUrl == null || tokenUrl.trim().isEmpty()) {
            log.info("No token URL configured for partner: {}. Skipping token retrieval.", partnerId);
            return;
        }

        Token token = tokenCache.get(partnerId, p -> {
            log.info("No valid token in cache for partner: {}. Fetching a new one.", p);
            try {
                log.info("Fetching new token for partner: {}", p);
                String clientId = getContext().resolvePropertyPlaceholders("{{partners." + p + ".client.id}}");
                String clientSecret = getContext().resolvePropertyPlaceholders("{{partners." + p + ".client.secret}}");
                String requestBody = "grant_type=client_credentials&client_id=" + clientId + "&client_secret=" + clientSecret;

                Map<String, Object> response = getContext().createProducerTemplate()
                    .requestBodyAndHeader("direct:getToken", requestBody, "tokenUrl", tokenUrl, Map.class);

                String accessToken = (String) response.get("access_token");
                long expiresIn = ((Number) response.getOrDefault("expires_in", 3600)).longValue();

                return new Token(accessToken, expiresIn);
            } catch (Exception e) {
                log.error("Failed to get token for partner: {}", p, e);
                exchange.setProperty("FailureException", e);
                String failReason = "Token retrieval failed";
                if (e != null && e.getMessage() != null) {
                    failReason += ": " + e.getMessage();
                }
                exchange.setProperty("FailureReason", failReason);
                throw new RuntimeException(e);
            }
        });

        if (token != null) {
            exchange.getIn().setHeader("Authorization", "Bearer " + token.accessToken);
        }
    }

    private void sendActivityLog(Exchange exchange) {
        try {
            String entryId = exchange.getProperty("EntryId", String.class);
            String paymentStatus = exchange.getIn().getHeader("paymentStatus", String.class);
            String callbackUrl = exchange.getProperty("CallbackUrl", String.class);
            String prefix = getContext().resolvePropertyPlaceholders("{{?kafka.prefix.topic}}");
            String topic = getContext().resolvePropertyPlaceholders("{{kafka.activity.topic}}");
            String activityTopic = (prefix != null ? prefix : "") + topic;

            Map<String, String> activityMessage = new HashMap<>();
            activityMessage.put("activity_type_id", "104400140");
            activityMessage.put("flow", entryId);
            activityMessage.put("step", "Send status to merchant");
            activityMessage.put("payment_status", paymentStatus != null ? paymentStatus : "Failed");
            activityMessage.put("callback_url", callbackUrl);
            
            String failReason = exchange.getProperty("FailureReason", String.class);
            if (failReason != null && !failReason.isEmpty()) {                
                if (failReason.length() > 500) {
                    failReason = failReason.substring(0, 497) + "...";
                }
                activityMessage.put("fail_reason", failReason);
                log.warn("Adding failure reason to activity log: {}", failReason);
            }

            getContext().createProducerTemplate().sendBodyAndHeader("direct:activityLog", activityMessage, "activityTopic", activityTopic);

        } catch (Exception e) {
            log.error("Failed to send activity log", e);
        }
    }

    private static class Token {
        final String accessToken;
        final long expiresIn;

        Token(String accessToken, long expiresIn) {
            this.accessToken = accessToken;
            this.expiresIn = expiresIn;
        }
    }

    private final Cache<String, Token> tokenCache = Caffeine.newBuilder()
        .expireAfter(new Expiry<String, Token>() {
            public long expireAfterCreate(String key, Token token, long currentTime) {
                return TimeUnit.SECONDS.toNanos(token.expiresIn > 0 ? token.expiresIn : 3600);
            }
            public long expireAfterUpdate(String key, Token token, long currentTime, long currentDuration) {
                return currentDuration;
            }
            public long expireAfterRead(String key, Token token, long currentTime, long currentDuration) {
                return currentDuration;
            }
        })
        .build();
}